import openmeteo_requests
import pandas as pd
import requests_cache
from retry_requests import retry
from mcp.server.fastmcp import FastMCP
from typing import Optional

mcp = FastMCP("Weather")


def fetch_hourly_temperature(latitude: float, longitude: float) -> pd.DataFrame:
    """
    Fetches hourly temperature data for the given latitude and longitude using the Open-Meteo API.

    Args:
        latitude (float): Latitude of the location.
        longitude (float): Longitude of the location.

    Returns:
        pd.DataFrame: A DataFrame containing hourly timestamps and temperature data.
    """
    # Setup the Open-Meteo API client with cache and retry on error
    cache_session = requests_cache.CachedSession('.cache', expire_after=3600)
    retry_session = retry(cache_session, retries=5, backoff_factor=0.2)
    # Type ignore for session compatibility
    openmeteo = openmeteo_requests.Client(
        session=retry_session)  # type: ignore

    url = "https://api.open-meteo.com/v1/forecast"
    params = {
        "latitude": latitude,
        "longitude": longitude,
        "hourly": "temperature_2m"
    }

    # Send API request
    responses = openmeteo.weather_api(url, params=params)
    response = responses[0]

    print(f"Coordinates {response.Latitude()}°N {response.Longitude()}°E")
    print(f"Elevation {response.Elevation()} m asl")
    print(f"Timezone {response.Timezone()}{response.TimezoneAbbreviation()}")
    print(f"Timezone difference to GMT+0 {response.UtcOffsetSeconds()} s")

    # Process hourly data
    hourly = response.Hourly()
    if hourly is None:
        raise ValueError("No hourly data available from the API response")

    # Get temperature variable and check if it exists
    temperature_var = hourly.Variables(0)
    if temperature_var is None:
        raise ValueError(
            "Temperature variable not available in the API response")

    hourly_temperature_2m = temperature_var.ValuesAsNumpy()

    hourly_data = {
        "date": pd.date_range(
            start=pd.to_datetime(hourly.Time(), unit="s", utc=True),
            end=pd.to_datetime(hourly.TimeEnd(), unit="s", utc=True),
            freq=pd.Timedelta(seconds=hourly.Interval()),
            inclusive="left"
        ),
        "temperature_2m": hourly_temperature_2m
    }

    hourly_dataframe = pd.DataFrame(data=hourly_data)
    return hourly_dataframe


def main():
    print("Hello from mcp-sever!")


if __name__ == "__main__":
    main()
